import { defineComponent, onMounted, PropType, ref } from 'vue';
import EquityChain from '@/shared/charts/equity-chain';
import QPlainTable from '@/components/global/q-plain-table';

const BenefitChart = defineComponent({
  name: 'BenefitChart',
  props: {
    viewData: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    const { ActualControllerName, ActualControllerKeyNo, Paths } = viewData;
    return (
      <QPlainTable>
        <tr>
          <td class="tb" width="180">
            实际控制人
          </td>
          <td colspan="5">
            <q-entity-link
              coy-obj={{
                Name: ActualControllerName,
                KeyNo: ActualControllerKeyNo,
              }}
            ></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="180">
            总持股比例
          </td>
          <td colspan="1" width="140">
            {viewData.PercentTotal || '-'}
          </td>
          <td class="tb" width="180">
            直接持股比例
          </td>
          <td colspan="1" width="140">
            {viewData.StockPercent || '-'}
          </td>
          <td class="tb" width="180">
            间接持股比例
          </td>
          <td colspan="1" width="140">
            {viewData.IndirectStockPercent || '-'}
          </td>
        </tr>
        <tr>
          <td>股权链</td>
          <td colspan="5">
            <div style={{ height: '450px' }}>
              <EquityChain />
            </div>
          </td>
        </tr>
      </QPlainTable>
    );
  },
});

export default BenefitChart;
