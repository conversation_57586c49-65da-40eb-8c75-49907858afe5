import { defineComponent, ref } from 'vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import QModal from '@/components/global/q-modal/q-modal';
import QTabs from '@/components/global/q-tabs';

import SimpleCompanyForm from '../widgets/company-by-name/form';
import styles from './add-company-modal.module.less';
import UserCountStatistics from '@/components/user-count-statistics';

const AddCompanyModal = defineComponent({
  name: 'AddCompanyModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    /** Tabs */
    const tabs = props.params.tabs || [
      { label: '添加单个企业', key: 'single' },
      { label: '批量上传', key: 'batch' },
    ];
    const currentTab = ref(tabs[0].key);

    /** Modal */
    const visible = ref(true);
    const groupsChange = ref(false);
    const onCancel = () => {
      visible.value = false;
      emit('resolve', {
        groupsChange: groupsChange.value,
      });
    };

    /** 选中值 */
    const selectedValues = ref<{ companyId: string; companyName: string }[]>([]);
    /**
     * 切换标签时，重置 selectedValues
     */
    const handleTabChange = () => {
      selectedValues.value = [];
    };

    /**
     * 添加企业
     */
    const formRef = ref<WrappedFormUtils>();
    const handleSelectFromCompany = async () => {
      return new Promise((resolve, reject) => {
        formRef.value?.validateFields((errors, { companyId, companyName, monitorGroupId }) => {
          if (errors) {
            reject(errors);
            return;
          }
          const value = {
            companyId,
            companyName,
            monitorGroupId,
          };
          selectedValues.value = [value]; // 统一结构: 对象转为数组
          resolve({
            newCompanies: [value],
            groupsChange: groupsChange.value,
          });
        });
      });
    };

    /** Actions */
    const onSubmit = async () => {
      try {
        if (currentTab.value === 'single') {
          await handleSelectFromCompany();
        }
        emit('resolve', {
          newCompanies: selectedValues.value,
          groupsChange: groupsChange.value,
        });
      } catch (e) {
        console.error(e);
      }
    };

    const handleUploadConfirm = (data) => {
      emit('resolve', data);
    };

    return {
      tabs,
      currentTab,
      visible,
      groupsChange,
      onCancel,
      onSubmit,
      selectedValues,
      formRef,
      handleTabChange,
      handleUploadConfirm,
    };
  },
  render() {
    return (
      <QModal
        {...{
          props: {
            onOk: this.onSubmit,
            visible: this.visible,
            wrapClassName: styles.container,
            destroyOnClose: true,
            footer: this.currentTab === 'batch' ? false : undefined,
          },
          on: {
            cancel: () => this.onCancel(),
          },
        }}
      >
        <div slot="title" class="flex items-center justify-between">
          <QTabs size="large" v-model={this.currentTab} tabs={this.tabs} onChange={this.handleTabChange} />
          <UserCountStatistics dimension="dimension" style={{ marginRight: '28px' }} />
        </div>
        <SimpleCompanyForm
          ref="formRef"
          originData={this.params.originData}
          modelList={this.params.modelList}
          onFreshGroup={() => {
            this.groupsChange = true;
          }}
          onUploadSuccess={this.handleUploadConfirm}
          addType={this.currentTab}
        />
      </QModal>
    );
  },
});

export default AddCompanyModal;
