import { computed, defineComponent } from 'vue';

import { useStore } from '@/store';

import styles from './user-count-statistics.module.less';

const labelMap = (dimension) => {
  switch (dimension) {
    case 'monitorCompanyQuantity':
      return '剩余监控额度';
    default:
      return '剩余企业数';
  }
};

const UserCountStatistics = defineComponent({
  props: {
    dimension: {
      type: String,
      default: 'batchDiligenceDailyQuantity',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    showLimit: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const store = useStore();
    const getUsage = () => {
      const countData = computed(() => store.getters['user/usage']);
      return {
        countData,
      };
    };
    const { countData } = getUsage();
    return {
      countData,
    };
  },
  render() {
    if (this.loading) {
      return <div></div>;
    }
    return (
      <div class={styles.content}>
        <div class="flex-between">
          <div>{labelMap(this.dimension)}：</div>
          <div>
            <span data-testid="remaining-count">{this.countData?.bundleUsage?.[this.dimension]?.stock || 0}</span>
            {this.showLimit ? <span>/</span> : null}
            {this.showLimit ? <span>{this.countData?.bundleUsage?.[this.dimension]?.limitation || 0}</span> : null}
          </div>
        </div>
      </div>
    );
  },
});

export default UserCountStatistics;
