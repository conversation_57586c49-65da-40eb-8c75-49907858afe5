import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router/composables';

import CompanySearchNew from './widgets/company-search-new';
import styles from './single.module.less';
import TipImg from './images/single.png';

const IdentifySinglePage = defineComponent({
  name: 'IdentifySinglePage',
  setup() {
    const selectCompany = ref<any>(null);

    const router = useRouter();
    const updateSelect = (data) => {
      selectCompany.value = data;
      router.push({
        name: 'ubo-identify-single-detail',
        params: { id: selectCompany.value.id },
        query: { name: selectCompany.value.value },
      });
    };
    const goToIdentify = () => {
      router.push({
        path: `single/${selectCompany.value.id}`,
      });
    };
    return {
      selectCompany,
      updateSelect,
      goToIdentify,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.content}>
          <div class={styles.title}>受益所有人识别</div>
          <CompanySearchNew
            hasBulkSearch={false}
            enterText="一键识别"
            onSelect={this.updateSelect}
            onSearch={this.goToIdentify}
          ></CompanySearchNew>
          <div class={styles.img}>
            <img src={TipImg} />
          </div>
        </div>
      </div>
    );
  },
});

export default IdentifySinglePage;
