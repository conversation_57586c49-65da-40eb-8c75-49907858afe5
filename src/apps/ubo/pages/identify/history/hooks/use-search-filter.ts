import { ref, computed, onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router/composables';
import { cloneDeep, isEqual, isNil, sortBy } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService, identify as identifyService } from '@/shared/services';

import { getFilterGroups } from '../config';

type FilterOptions = {
  label: string;
  value: string | number;
  count?: number;
};

export const useSearchFilter = () => {
  const route = useRoute();

  const aggsOptionSearch = useRequest(monitorService.searchDynamics);
  const searchHistory = useRequest(identifyService.getVerificationRecord);

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const isInit = ref(false);

  /** 公司数量汇总 */
  const totalCompany = computed(() => {
    const data = searchHistory.data.value?.aggsResponse || {};
    return data['4_companyCount']?.value || 0;
  });

  /** 表格数据 */
  const dataSource = computed(() => {
    const data = searchHistory.data.value || {};
    return data.data || [];
  });

  const pagination = ref({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  const previewQuery = ref<Record<string, any>>({});

  const query = ref<Record<string, any>>(cloneDeep(defaultFilterValues));

  const sort = ref({});

  const isFilterLoading = ref(false);
  const filterOptions = reactive<{
    operators: FilterOptions[];
  }>({
    operators: [],
  });

  /** 更新搜索选项卡 */
  const getFilterOptions = (group?, showAll = false) => {

  };
  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return getFilterGroups(filterOptions);
  });

  const search = async (payload?: Record<string, any>) => {
    const res = await searchHistory.execute({
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...query.value.filters,
      keywords: query.value.keywords,
      createDate: query.value.filters?.createDate ? [query.value.filters.createDate] : undefined,
      ...sort.value,
      ...payload,
    });
    previewQuery.value = cloneDeep(query.value);
    pagination.value.total = res?.total > 50000 ? 50000 : res?.total;
    pagination.value.current = res?.pageIndex;
    pagination.value.pageSize = res?.pageSize;
  };

  /** groupId改变时，重置其他筛选项 */
  const resetOtherFilters = (remainedKeys: string[], currentFilters = {}) => {
    const filters = remainedKeys.reduce((acc, key) => {
      acc[key] = currentFilters[key];
      return acc;
    }, {});
    return {
      ...defaultFilterValues,
      filters,
    };
  };

  /** 更新搜索过滤 */
  const handleFilterChange = async (values) => {
    const { filters } = values;
    let updateOptions = false;
    if (!isEqual(filters?.groupId, previewQuery.value.filters?.groupId)) {
      query.value = resetOtherFilters(['groupId'], filters);
      updateOptions = true;
    } else {
      query.value = values;
    }
    pagination.value.current = 1;
    await search();
    isFilterLoading.value = false;
    if (updateOptions) {
      getFilterOptions();
    }
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    query.value = {
      ...defaultFilterValues,
    };
  };
  /** 初始化 */
  onMounted(async () => {
    try {
      await aggsOptionSearch.execute<any>({ aggsField: [4] });
      await searchHistory.execute();
    } catch (error) {
      console.error(error);
    }
  });

  return {
    filterValues: query,
    filterGroups,
    handleFilterChange,
    handleFilterReset,
    isFilterLoading,
    isLoading: false,
    getFilterOptions,
    totalCompany,
    dataSource,
    pagination,
    search,
    isInit,
    sortInfo: sort,
  };
};
