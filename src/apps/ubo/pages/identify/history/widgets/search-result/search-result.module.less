.container {
  .extra {
    display: flex;
    gap: 12px;

    .dropdownBtn {
      display: flex;
      align-items: center;

      :global {
        i {
          margin-right: 5px;
          font-size: 16px;
        }
      }
    }

    .dropdown {
      border-color: #128bed;
      color: #128bed;
      background-color: #f2f8fe;

      &:hover {
        background-color: #E2F1FD;
      }
    }
  }
}

.popTip{
  :global{
    .ant-tooltip-content,
    .ant-tooltip-inner{
      background-color: #ffff;
      color: #333;
    }

    .ant-tooltip-arrow{
      width: 8px;
      height: 8px;

      &::before{
        width: 9px;
        height: 9px;
        background: #fff;
      }
    }
  }
}
