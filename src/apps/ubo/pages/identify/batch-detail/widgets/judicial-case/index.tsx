import { defineComponent } from 'vue';
import { dateFormat } from '@/utils/format';
import QRoleText from '@/components/global/q-role-text';
import { get } from 'lodash';
import styles from './judicial-case.module.less';

const JudicialCase = defineComponent({
  name: 'JudicialCase',
  props: {
    record: {
      type: Object,
      required: true,
    },
    keyNo: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const JudicialCaseMergedColumns = [
      {
        title: '进程日期',
        width: 110,
        dataIndex: 'Date',
        customRender: (date) => {
          return dateFormat(date);
        },
      },
      {
        title: '案件进程',
        width: 90,
        dataIndex: 'TrialRound',
      },
      {
        title: '案件身份',
        width: 150,
        dataIndex: 'CaseRole',
        customRender: (role) => {
          if (!role?.length) return '-';
          return role
            .filter((r) => r.KeyNo === props.keyNo)
            .map((item) => {
              return (
                <div>
                  {item.Role}
                  <QRoleText roleD={item.LRD} />
                </div>
              );
            });
        },
      },
      {
        title: '案号',
        width: 220,
        dataIndex: 'CaseNo',
      },
      {
        title: '法院',
        width: 160,
        dataIndex: 'Court',
      },
    ];

    return {
      JudicialCaseMergedColumns,
    };
  },
  render() {
    const renderJudicialCaseContent = (record) => {
      const result = record.TrailRoundDetails.map((data) => {
        const row = this.JudicialCaseMergedColumns.map((c) => {
          if (c.customRender) {
            return (
              <td class={styles.cell} style={{ width: `${c.width}px` }}>
                {c.customRender(get(data, c.dataIndex))}
              </td>
            );
          }
          return (
            <td class={styles.cell} style={{ width: `${c.width}px` }}>
              {get(data, c.dataIndex)}
            </td>
          );
        });
        return <tr class={styles.row}>{row}</tr>;
      });
      return (
        <table class={styles.container}>
          <tbody>{result}</tbody>
        </table>
      );
    };
    return renderJudicialCaseContent(this.record);
  },
});

export default JudicialCase;
