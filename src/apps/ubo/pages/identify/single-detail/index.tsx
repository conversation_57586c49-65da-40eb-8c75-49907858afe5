import { defineComponent, computed, ref } from 'vue';
import { Breadcrumb, message as Message, Tooltip } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router/composables';
import styles from './single-detail.page.module.less';
import HeroicLayout from '@/shared/layouts/heroic';
import { ResultOverview } from './includes/result-overview/index';
import StandardOne from './includes/standard-one/index';
import StandardTwo from './includes/standard-two/index';
import StandardThree from './includes/standard-three/index';
import ManagePersons from './includes/manage-persons/index';
import BusinessInfo from './includes/business-info/index';
import StandardListBase from './includes/standard-list-base';
import CompanyHeader from './includes/company-header/index';
import CompanyStatus from '@/components/global/q-company-status';
import SimpleResult from './includes/simple-result/index';
import { identify as identifyService, company as companyService } from '@/shared/services';
import Empty from '@/shared/components/empty';
import { getCompanyFields } from './config';
import { getBaseInfoTitle } from './includes/business-info/utils';

interface IResultInfo {
  companyName?: string;
  keyNo?: string;
  beneType?: string;
  breakThroughList?: any[]; //标准1 穿透数据
  criterion2ndInfo?: {
    criterion2ndDetailList?: any[];
    remark?: string;
  }; //标准2 实控人数据
  criterion3rdInfo?: {
    criterion3rdDetailList?: any[];
    remark?: string;
  }; //标准3 控制/影响自然人
  managerList?: any[]; //日常管理人员
  fundManagerList?: any[]; //基金 预留
  recordType?: string;
  operName?: string;
  operKeyNo?: string;
  position?: string;
  resultType?: string;
  subjectType?: string | null;
  remark?: string;
}

function getBodyStyle({ paddingTop }) {
  const defaultStyle = {
    display: 'flex',
    flexDirection: 'column',
    paddingTop,
    backgroundColor: '#fff',
    borderRadius: 0,
  };
  return defaultStyle;
}

const IdentifyDetailPage = defineComponent({
  name: 'IdentifyDetailPage',
  props: {
    id: {
      type: String,
      required: true,
    },
    isExternal: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const standardOneTotalCount = ref(0);
    const isFundType = false; //是否是基金穿透
    const loading = ref(true);

    const resultInfo = ref<IResultInfo>({});

    const updateStandardOneTotalCount = (count: number) => {
      standardOneTotalCount.value = count;
    };
    const beneType = computed(() => {
      const beneType = resultInfo.value?.beneType;
      if (beneType === 'N') {
        //处理是否是无数据的情况
        return (resultInfo.value?.breakThroughList?.length ?? 0) > 0 ||
          (resultInfo.value?.criterion2ndInfo?.criterion2ndDetailList?.length ?? 0) > 0 ||
          (resultInfo.value?.criterion3rdInfo?.criterion3rdDetailList?.length ?? 0) > 0 ||
          (resultInfo.value?.managerList?.length ?? 0) > 0
          ? beneType
          : 'NE';
      }
      return beneType || '';
    });
    const hideDailyManager = computed(() => {
      switch (beneType.value) {
        case 'N':
          return (
            (resultInfo.value?.breakThroughList?.length ?? 0) > 0 ||
            (resultInfo.value?.criterion2ndInfo?.criterion2ndDetailList?.length ?? 0) > 0 ||
            (resultInfo.value?.criterion3rdInfo?.criterion3rdDetailList?.length ?? 0) > 0
          );
        case 'S':
          return !isFundType || (resultInfo.value?.fundManagerList?.length ?? 0) > 0;
        default:
          return false;
      }
    });
    const businessInfo = ref<{ [key: string]: any }>({});

    Promise.all([
      identifyService.getSingleDetail({ companyKey: route.query.name as string }),
      companyService.getDetail({ keyNo: route.params.id }, { fields: getCompanyFields(route.params.id) }) as Promise<{
        [key: string]: any;
      }>,
    ]).then(([res, res2]) => {
      resultInfo.value = res?.uboDetails || {};
      resultInfo.value.subjectType = res?.subjectType || '';
      res2.Name ??= route.query.name as string;
      res2.KeyNo ??= route.params.id as string;
      businessInfo.value = res2 || {};

      loading.value = false;
    });

    return {
      loading,
      route,
      router,

      resultInfo,
      updateStandardOneTotalCount,
      hideDailyManager,
      businessInfo,
      beneType,
    };
  },
  render() {
    const { loading, router, businessInfo, beneType, resultInfo } = this;
    const routeList: any = [
      {
        id: 'home',
        route: {
          name: 'ubo-identify-single',
        },
        name: '单一识别',
      },
    ];

    // 是否有面包屑
    const hasBreadcrumb = !this.isExternal && routeList.length > 0;
    const breadcrumbHeight = hasBreadcrumb ? 50 : 0;
    const bodyPaddingTop = breadcrumbHeight > 0 ? 0 : breadcrumbHeight; // 需要考虑容器中的 paddingTop
    // 布局样式
    const bodyStyle = getBodyStyle({
      paddingTop: `${bodyPaddingTop}px`,
    });

    const {
      breakThroughList,
      criterion2ndInfo: actualPersonList,
      criterion3rdInfo,
      managerList: managePersons,
      companyName,
      keyNo,
      remark,
      recordType,
      subjectType,
    } = resultInfo;
    const simpleDataList = [resultInfo];
    /**
     * beneType:
     * - N: 可正常识别，展示标准一、二、三、日常经营人员、工商信息
     * - E: 可豁免识别, 只展示描述信息
     * - S：可简易识别，只展示结果表格
     * - NE：识别结果为空，展示登记信息/工商信息
     */
    const generateLayout = () => {
      switch (beneType) {
        case 'N': //可正常识别，展示标准一、二、三、日常经营人员、工商信息
          return (
            <div class={styles.standardList}>
              <StandardOne
                list={breakThroughList}
                keyNo={keyNo}
                on={{
                  totalCountUpdate: this.updateStandardOneTotalCount,
                }}
              />
              <StandardTwo list={actualPersonList?.criterion2ndDetailList || []} keyNo={keyNo} name={companyName} />
              <StandardThree list={criterion3rdInfo?.criterion3rdDetailList || []} keyNo={keyNo} />
              {managePersons?.length ? (
                <ManagePersons list={managePersons} keyNo={keyNo} name={companyName} hideDailyManager={this.hideDailyManager} />
              ) : (
                ''
              )}
              <StandardListBase title={getBaseInfoTitle(keyNo)}>
                <BusinessInfo businessInfo={businessInfo} keyNo={keyNo}></BusinessInfo>
              </StandardListBase>
            </div>
          );
        case 'E': //可豁免识别, 只展示描述信息
          return (
            <div class="text-center pt-132px">
              <div class="w-800px inline-block bg-#F7F7F7 p-15px leading-22px text-#666 text-left rounded-4px">{remark || ''}</div>
            </div>
          );
        case 'S': //可简易识别，只展示结果表格
          return (
            <div class="p-15px">
              <SimpleResult list={simpleDataList}></SimpleResult>
              {(managePersons?.length ?? 0) > 0 ? (
                <ManagePersons
                  list={managePersons}
                  keyNo={keyNo}
                  name={companyName}
                  hideDailyManager={this.hideDailyManager}
                  hideBodyTip="继续展示"
                />
              ) : (
                ''
              )}
              <StandardListBase title={getBaseInfoTitle(keyNo)}>
                <BusinessInfo businessInfo={businessInfo} keyNo={keyNo}></BusinessInfo>
              </StandardListBase>
            </div>
          );
        case 'NE': //识别结果为空，展示登记信息/工商信息
          return (
            <div class="p-15px">
              <StandardListBase title={getBaseInfoTitle(keyNo)}>
                <BusinessInfo businessInfo={businessInfo} keyNo={keyNo}></BusinessInfo>
              </StandardListBase>
            </div>
          );
        default:
          return <Empty />;
      }
    };

    return loading ? (
      <HeroicLayout
        innerStyle={{ minHeight: this.isExternal ? '100vh' : `calc(100vh - 52px - 20px)` }}
        bodyStyle={bodyStyle}
        loading={loading}
        gap={false}
      ></HeroicLayout>
    ) : (
      <div class={styles.container}>
        {!this.isExternal && companyName && routeList?.length ? (
          <div class="flex justify-between items-center sticky-breadcrumb">
            <div>
              <Breadcrumb>
                {routeList.map((v: any, index: number) => {
                  return (
                    <Breadcrumb.Item key={v.id}>
                      <a
                        onClick={() => {
                          // 上级页面有window.open的行为，返回上一页不可用
                          if (v.route === 'back' || v.route?.path === 'back') {
                            router.back();
                          } else {
                            router.push(v.route);
                          }
                        }}
                      >
                        {index === 0 ? <q-icon type="icon-mianbaoxiefanhui"></q-icon> : null}
                        {v.name}
                      </a>
                    </Breadcrumb.Item>
                  );
                })}
                <Breadcrumb.Item>
                  {companyName}
                  {businessInfo?.creditCode ? `（${businessInfo?.creditCode}）` : ''}
                  {businessInfo?.shortStatus ? <CompanyStatus status={businessInfo?.shortStatus} /> : ''}
                </Breadcrumb.Item>
              </Breadcrumb>
            </div>
          </div>
        ) : null}
        <CompanyHeader
          businessInfo={businessInfo}
          keyNo={keyNo}
          recordType={recordType}
          beneType={beneType}
          subjectType={subjectType || ''}
        ></CompanyHeader>
        <ResultOverview item={{ beneType }} class="mt-2.5" tip={beneType !== 'E' ? remark || '' : ''}></ResultOverview>
        <HeroicLayout
          innerStyle={{ minHeight: this.isExternal ? '100vh' : `calc(100vh - 52px - 60px - 118px - 50px - 10px)` }}
          bodyStyle={bodyStyle}
          loading={loading}
          gap={false}
        >
          {generateLayout()}
        </HeroicLayout>
      </div>
    );
  },
});

export default IdentifyDetailPage;
