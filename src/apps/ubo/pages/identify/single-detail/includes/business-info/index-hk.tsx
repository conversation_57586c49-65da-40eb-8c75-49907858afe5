/**
 * 香港企业信息
 */
import { defineComponent, PropType, getCurrentInstance, VNode } from 'vue';
import QPlainTable from '@/components/global/q-plain-table';
import { HK_DATA } from './mock-data';
import QIcon from '@/components/global/q-icon';
import QEntityLink from '@/components/global/q-entity-link';

interface Iitem {
  key: string;
  cnName: string;
  type: string;
  canHide?: boolean;
  specialType?: string;
  getValue?: (findValue: (key: string) => string, vm: any) => string | VNode | null;
}
interface IRow {
  type: 'full-width' | 'two-items' | 'single-item';
  items: Iitem[];
  layout: { item: Iitem; colspan: number }[];
}
/**
 * 创建企业名称配置项
 * @param isFund 是否为基金类型
 * @param isSimple 是否为简单模式（返回字符串而非JSX）
 */
const createCompanyNameItem = (isFund = false, isSimple = false): Iitem => ({
  key: 'CompName',
  cnName: isFund ? '基金名称' : '企业名称',
  type: 'custom',
  getValue: (findValue) => {
    const compName = findValue('CompName');
    const compNameEn = findValue('CompNameEn');
    const names = [compName, compNameEn].filter((val) => val);

    if (!names.length) return null;

    if (isSimple) {
      return names.join('<br/>');
    }

    return (
      <div>
        {names.map((name, index) => (
          <div key={index}>{name}</div>
        ))}
      </div>
    );
  },
});

/**
 * 创建董事长配置项
 */
const createCompRepresentItem = (): Iitem => ({
  cnName: '董事长',
  key: 'CompRepresentList',
  type: 'custom',
  canHide: true,
  getValue: (findValue) => {
    const value = findValue('CompRepresentList');
    const list = JSON.parse(value || '[]');
    if (list.length) {
      return (
        <div>
          {list.map((val, index) => (
            <span key={val.KeyNo || index}>
              <QEntityLink keyNo={val.KeyNo} name={val.Name} />
              {index < list.length - 1 && '、'}
            </span>
          ))}
        </div>
      );
    }
    return null;
  },
});

/**
 * 创建电话配置项
 */
const createPhoneItem = (): Iitem => ({
  key: 'ContactPhones',
  cnName: '电话',
  type: 'custom',
  canHide: true,
  // ellipsis: 3,
  getValue: (findValue) => {
    let contacti: { [key: string]: any } = {};
    try {
      contacti = JSON.parse(findValue('ContactInfo') || '{}');
    } catch (error) {}
    const now = contacti?.PhoneNumber;

    let contacth: { [key: string]: any } = [];
    try {
      contacth = JSON.parse(findValue('ContactPhones') || '[]');
    } catch (error) {}
    const his = contacth.map((val) => val.Tel) || [];

    const phoneList = [now, ...his].filter((val) => val);
    return phoneList.length ? phoneList.join('<br>') : null;
  },
});

/**
 * 创建LEI码配置项
 * @param keyNo 企业编号
 */
const createLEICodeItem = (keyNo): Iitem => ({
  key: 'LEICode',
  cnName: '全球法人识别编码（LEI）',
  type: 'special',
  canHide: true,
  getValue: (findValue, vm) => {
    const value = findValue('LEICode');
    if (!value) return null;
    return (
      <div>
        {value}
        <a
          class="ml-2.5 text-#128bed"
          onClick={() => {
            vm?.$modal.showDimension('LEIDetail', { keyNo });
          }}
        >
          详情<QIcon type="icon-wenzilianjiantou"></QIcon>
        </a>
      </div>
    );
  },
});

/**
 * 创建企业曾用名配置项
 * @param isFund 是否为基金类型
 */
const createCompNameHisItem = (isFund = false): Iitem => ({
  cnName: isFund ? '基金曾用名' : '企业曾用名',
  key: 'CompNameHis',
  type: 'special',
  canHide: true,
  getValue: (findValue) => {
    const value = findValue('CompNameHis');
    try {
      const oldNameArr = JSON.parse(value || '[]');
      if (!oldNameArr.length) return null;

      return oldNameArr.map((v, index) => {
        let dateRange = v.StartDate || v.EndDate ? [v.StartDate, v.EndDate].join(' 至 ') : '';
        dateRange = dateRange ? `（${dateRange}）` : '';
        return (
          <div key={index}>
            {v.NameHis ? (
              <div>
                {v.NameHis}
                {!v.NameHisEn && dateRange}
              </div>
            ) : null}
            {v.NameHisEn ? (
              <div>
                {v.NameHisEn}
                {dateRange}
              </div>
            ) : null}
          </div>
        );
      });
    } catch {
      return null;
    }
  },
});

/**
 * 创建基础配置项
 * @param key 字段键
 * @param cnName 中文名称
 * @param options 额外选项
 */
const createBasicItem = (key, cnName, options = {}): Iitem => ({
  key,
  cnName,
  type: 'normal',
  ...options,
});

/**
 * 生成包含实际值的显示列表，并过滤掉无值的项目
 * @param keyNo - 企业编号
 * @param businessInfo - 业务信息对象
 * @returns 过滤后的显示项目数组
 */
/**
 * 获取香港企业基本信息表格配置列表
 * @param keyNo - 企业关键编号
 * @param businessInfo - 企业信息数据
 * @returns 处理后的表格配置数组
 */
/**
 * 生成股票企业的显示列表配置
 * @param keyNo - 企业编号
 */
const getSortList = (keyNo): Iitem[] => {
  // 定义所有可能的数据项配置
  return [
    createBasicItem('RegNo', '商业登记号码'),
    createCompanyNameItem(),
    createBasicItem('CompNo', '企业编号', { canHide: true }),
    createCompRepresentItem(),
    createBasicItem('CompStatusDetail', '企业状态', { canHide: true }),
    createBasicItem('CompStartDate', '成立日期', { canHide: true }),
    createBasicItem('RegisteredCapital', '股本', { canHide: true }),
    createBasicItem('CompTypeDetail', '企业类型', { canHide: true }),
    createBasicItem('RegPlaceDetail', '成立地方', { canHide: true }),
    {
      cnName: '押记登记册',
      key: 'Mortgage',
      type: 'custom',
      getValue: (findValue) => {
        const value = findValue('Mortgage');
        return value !== '无' ? value : '';
      },
      canHide: true,
    },
    createBasicItem('Remarks', '备注', { canHide: true }),
    createBasicItem('LiquidationMode', '清盘模式', { canHide: true }),
    createLEICodeItem(keyNo),
    createCompNameHisItem(),
    {
      cnName: '企业地址',
      key: 'ContactAddress',
      type: 'special',
      specialType: 'address',
      canHide: true,
    },
    {
      cnName: '重要事项',
      key: 'RegImpmatters',
      type: 'special',
      specialType: 'important_matters',
      canHide: true,
    },
  ];
};

/**
 * 获取香港企业非股票信息表格配置列表
 * @param keyNo - 企业关键编号
 * @param businessInfo - 企业信息数据
 * @param dataList - 数据列表
 * @param headInfo - 头部信息
 * @returns 处理后的表格配置数组
 */
/**
 * 生成非股票企业的显示列表配置
 * @param keyNo - 企业编号
 * @param businessInfo - 企业信息
 */
const getSortListUnStock = (keyNo, businessInfo): Iitem[] => {
  const dataList = businessInfo?.Data || [];
  const headInfo = businessInfo?.HeadInfo;

  // 判断是否为基金类型
  const ctd = dataList?.find((item) => item.name === 'CompTypeDetail');
  const fund = ctd?.value === '有限合伙基金';

  // 判断是否为解散状态
  const dissStatus = ['HK-DISS', 'HK-EXAD'].includes(headInfo?.Status);

  // 定义所有可能的数据项配置
  const allItems = [
    createBasicItem('RegNo', '商业登记号码'),
    createCompanyNameItem(fund, true), // 使用简单模式返回字符串
    createBasicItem('CompNo', '企业编号', { canHide: true }),
    createBasicItem('CompStatusDetail', fund ? '基金状态' : '企业状态'),
    createBasicItem('CompStartDate', '成立日期'),
    createBasicItem('CompTypeDetail', fund ? '基金类型' : '企业类型'),
    createPhoneItem(),
    createBasicItem('ContactWebsite', '官网', { canHide: true }),
    createBasicItem('ContactEmail', '邮箱', { canHide: true }),
    createBasicItem('RegPlaceDetail', '成立地方', { canHide: true }),
    createBasicItem('CompDisDate', '解散/不再是独立实体日期', {
      canHide: true,
      forceHide: !dissStatus, // 根据状态强制隐藏
    }),
    {
      key: 'RegAddress',
      cnName: '办事处地址',
      type: 'special',
      specialType: 'address',
      canHide: true,
    },
    {
      key: 'RegImpmatters',
      cnName: '重要事项',
      type: 'special',
      specialType: 'important_matters',
      ellipsis: 2,
      canHide: true,
    },
    createLEICodeItem(keyNo),
    createCompNameHisItem(fund),
    {
      key: 'Remarks',
      cnName: '备注',
      type: 'special',
      specialType: 'remarks',
      ellipsis: 3,
      canHide: true,
    },
  ];
  return allItems;
};

export default defineComponent({
  name: 'BusinessInfoHk',
  props: {
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      required: true,
      default: () => HK_DATA,
    },
    keyNo: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const vm = getCurrentInstance()?.proxy;
    const findValue = (nameIndex): string => {
      return props.businessInfo?.Data?.find((v) => v.Name === nameIndex)?.Value || '';
    };

    const processedItems = () => {
      // 处理每个数据项，获取实际值并过滤无值项目
      const processedItems = getSortListUnStock(props.keyNo, props.businessInfo).map((item) => {
        let actualValue;

        if (item.getValue) {
          // 自定义获取值的逻辑
          actualValue = item.getValue(findValue, vm);
        } else {
          // 普通字段直接获取值
          actualValue = findValue(item.key);
        }

        return {
          ...item,
          actualValue,
          hasValue: actualValue !== null && actualValue !== undefined && actualValue !== '',
        };
      });

      // 过滤掉无值的可隐藏项目
      const filteredItems = processedItems.filter((item) => {
        if (item.canHide && !item.hasValue) {
          return false; // 过滤掉可隐藏且无值的项目
        }
        return true;
      });
      return filteredItems;
    };

    const showList = processedItems();

    return {
      findValue,
      vm,
      showList,
    };
  },
  render() {
    const { showList } = this;

    /**
     * 根据数据项获取显示值
     * @param item - showList中的数据项
     * @returns 渲染的内容
     */
    const getItemValue = (item) => {
      // 直接返回已经处理好的 actualValue
      return item.actualValue || '-';
    };

    // showList 已经过滤了无值的可隐藏项目，直接使用
    const enabledItems = showList;

    /**
     * 将项目按行分组，确保表格保持4列结构，基于新的数据结构
     * @param items - 启用的项目列表
     * @returns 分组后的行数组，每行包含布局信息
     */
    const groupItemsIntoRows = (items) => {
      const rows: IRow[] = [];
      let i = 0;

      while (i < items.length) {
        const currentItem = items[i];

        // 检查是否为特殊项目，需要跨列显示
        if (currentItem.type === 'special') {
          rows.push({
            type: 'full-width',
            items: [currentItem],
            layout: [{ item: currentItem, colspan: 3 }], // 标签1列，值跨3列
          });
          i++;
        } else {
          // 普通项目，尝试在同一行放置两个
          const nextItem = items[i + 1];

          if (nextItem && nextItem.type !== 'special') {
            // 两个普通项目可以放在同一行
            rows.push({
              type: 'two-items',
              items: [currentItem, nextItem],
              layout: [
                { item: currentItem, colspan: 1 }, // 标签1列，值1列
                { item: nextItem, colspan: 1 }, // 标签1列，值1列
              ],
            });
            i += 2;
          } else {
            // 单个普通项目，后面补空白
            rows.push({
              type: 'single-item',
              items: [currentItem],
              layout: [{ item: currentItem, colspan: 1 }],
            });
            i++;
          }
        }
      }

      return rows;
    };

    const rows = groupItemsIntoRows(enabledItems);

    return (
      <QPlainTable>
        <colgroup>
          <col width="22%" />
          <col width="28%" />
          <col width="22%" />
          <col width="28%" />
        </colgroup>
        <tbody>
          {rows.map((row, rowIndex) => {
            const { type, layout } = row;

            return (
              <tr key={rowIndex}>
                {(() => {
                  const cells: Array<VNode | Element> = [];

                  if (type === 'full-width') {
                    // 特殊项目：标签1列，值跨3列
                    const item = layout[0].item;
                    cells.push(
                      <td key={`${rowIndex}-${item.key}-label`} class="tb">
                        {item.cnName}
                      </td>
                    );
                    cells.push(
                      <td key={`${rowIndex}-${item.key}-value`} colspan="3">
                        {getItemValue(item)}
                      </td>
                    );
                  } else if (type === 'two-items') {
                    // 两个普通项目：每个占2列（标签1列+值1列）
                    for (const layoutItem of layout) {
                      const { item } = layoutItem;
                      cells.push(
                        <td key={`${rowIndex}-${item.key}-label`} class="tb">
                          {item.cnName}
                        </td>
                      );
                      cells.push(<td key={`${rowIndex}-${item.key}-value`}>{getItemValue(item)}</td>);
                    }
                  } else if (type === 'single-item') {
                    // 单个普通项目：标签1列，值1列，后面补2个空列
                    const item = layout[0].item;
                    cells.push(
                      <td key={`${rowIndex}-${item.key}-label`} class="tb">
                        {item.cnName}
                      </td>
                    );
                    cells.push(<td key={`${rowIndex}-${item.key}-value`}>{getItemValue(item)}</td>);
                    // 补充空白列
                    cells.push(<td key={`${rowIndex}-empty-label`} class="tb"></td>);
                    cells.push(<td key={`${rowIndex}-empty-value`}></td>);
                  }

                  return cells;
                })()}
              </tr>
            );
          })}
        </tbody>
      </QPlainTable>
    );
  },
});
