/**
 * 社会组织
 */
import { defineComponent, PropType, computed } from 'vue';
import QPlainTable from '@/components/global/q-plain-table';
import { toDash, timeStamp2Date, rmRMB } from './utils';
import QEntityLink from '@/components/global/q-entity-link';
import CompanyLogo from '@/components/company-logo';
import QIcon from '@/components/global/q-icon';

const resetArea = (area?: string | any) => {
  if (area) {
    if (area.Province || area.City || area.County) {
      return `${area.Province || ''}${area.City || ''}${area.County || ''}` || '-';
    }
    return area
      ?.replace?.('北京市北京市', '北京市')
      .replace?.('上海市上海市', '上海市')
      .replace?.('天津市天津市', '天津市')
      .replace?.('重庆市重庆市', '重庆市');
  } else {
    return '';
  }
};

export default defineComponent({
  name: 'BusinessInfo',
  props: {
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      default: () => {},
    },
    keyNo: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const directCity = ['北京市', '天津市', '上海市', '重庆市'];
    const companyArea = computed(() => {
      let str = '';
      if (props.businessInfo?.Area) {
        if (!directCity.includes(props.businessInfo.Area?.City)) {
          str += props.businessInfo.Area?.Province || '';
        }
        str += `${props.businessInfo.Area?.City || ''}${props.businessInfo.Area?.County || ''}`;
      }
      return str;
    });
    return { companyArea };
  },
  render() {
    const { businessInfo, keyNo, companyArea } = this;
    const isSocialOrg = keyNo?.startsWith('s'); //社会组织

    return (
      <div>
        <QPlainTable>
          <colgroup>
            <col width="13%" />
            <col width="21%" />
            <col width="13%" />
            <col width="20%" />
            <col width="13%" />
            <col width="20%" />
          </colgroup>
          <tbody>
            <tr>
              <td class="tb">法定代表人</td>
              <td>
                {businessInfo?.Oper?.Name ? (
                  <div class="flex">
                    <CompanyLogo
                      src={businessInfo?.Oper.ImageUrl}
                      id={businessInfo.Oper.KeyNo}
                      name={businessInfo.Oper.Name}
                      hasimage={businessInfo?.Oper.HasImage ? 1 : 0}
                      size="40px"
                      class="shrink-0"
                    ></CompanyLogo>
                    <div class="ml-2.5 flex flex-col">
                      <QEntityLink
                        coyObj={{
                          KeyNo: businessInfo.Oper?.KeyNo,
                          Name: businessInfo.Oper?.Name,
                          Org: businessInfo.Oper?.Org,
                        }}
                      />
                      {businessInfo?.Oper?.CompanyCount ? (
                        <a
                          class="text-#FF722D mt-1 cursor-pointer"
                          target="_blank"
                          href={`/embed/beneficaryDetail?personId=${businessInfo.Oper.KeyNo}&title=${encodeURIComponent(businessInfo.Oper.Name)}`}
                        >
                          关联{businessInfo.Oper.CompanyCount}家企业<QIcon type="icon-wenzilianjiantou"></QIcon>
                        </a>
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                ) : (
                  <div>-</div>
                )}
              </td>
              <td class="tb">登记状态</td>
              <td>{toDash(businessInfo?.Status)}</td>
              <td class="tb">成立日期</td>
              <td>{toDash(timeStamp2Date(businessInfo?.StartDate))}</td>
            </tr>
            <tr>
              <td class="tb">{isSocialOrg ? '注册资金' : '注册资本'}</td>
              <td>{rmRMB(toDash(businessInfo?.RegistCapi))}</td>
              <td class="tb">统一社会信用代码</td>
              <td>{toDash(businessInfo?.CreditCode)}</td>
              <td class="tb">社会组织类型</td>
              <td>{toDash(businessInfo?.EconKind)}</td>
            </tr>
            {businessInfo?.CharityOrg === 1
              ? [
                  <tr key="charity-1">
                    <td class="tb">业务主管单位</td>
                    <td>{toDash(businessInfo?.BelongOrg)}</td>
                    <td class="tb">慈善组织认定登记日期</td>
                    <td>{toDash(businessInfo?.RecognizeDate)}</td>
                    <td class="tb">是否取得公益性捐赠税前扣除资格</td>
                    <td>{businessInfo?.PretaxDeduction === 1 ? '是' : '否'}</td>
                  </tr>,
                  <tr key="charity-2">
                    <td class="tb">税前扣除资格有效期</td>
                    <td>{toDash(businessInfo?.PretaxDeductionValidity)}</td>
                    <td class="tb">是否具有公开募捐资格</td>
                    <td>{businessInfo?.PublicFundraise === 1 ? '是' : '否'}</td>
                    <td class="tb">曾用名</td>
                    <td class="">{toDash(businessInfo?.OriginalName?.[0]?.Name)}</td>
                  </tr>,
                  <tr key="charity-3">
                    <td class="tb">所属地区</td>
                    <td>{companyArea}</td>
                    <td class="tb">住所</td>
                    <td class="" colspan="3">
                      {toDash(businessInfo?.Address)}
                    </td>
                  </tr>,
                ]
              : [
                  <tr key="normal-1">
                    <td class="tb">业务主管单位</td>
                    <td>{toDash(businessInfo?.BelongOrg)}</td>
                    <td class="tb">登记管理机关</td>
                    <td>{toDash(businessInfo?.FazhengAuthority)}</td>
                    <td class="tb">证书有效期</td>
                    <td class="">{toDash(businessInfo?.CertificatePeriod)}</td>
                  </tr>,
                  <tr key="normal-2">
                    <td class="tb">所属地区</td>
                    <td>{toDash(companyArea)}</td>
                    <td class="tb">证书状态</td>
                    <td>{toDash(businessInfo?.CertificateStatus)}</td>
                    <td class="tb">曾用名</td>
                    <td class="">{toDash(businessInfo?.OriginalName?.[0]?.Name)}</td>
                  </tr>,
                  <tr key="normal-3">
                    <td class="tb">住所</td>
                    <td class="" colspan="5">
                      {toDash(businessInfo?.Address)}
                    </td>
                  </tr>,
                ]}
            <tr>
              <td class="tb">业务范围</td>
              <td colspan={5}>
                <div domPropsInnerHTML={businessInfo?.Scope || '-'}></div>
              </td>
            </tr>
          </tbody>
        </QPlainTable>
      </div>
    );
  },
});
