<template>
  <app-popup
    ref="popRef"
    :title="title"
    class="app-popup invest-detail"
    :disableBodyScroll="false"
    :options="{ width: '960px' }"
    :buttonOptions="[]"
  >
    <table v-if="viewData" class="ntable" :key="keyNo">
      <tr v-if="stockName">
        <td class="tb" width="20%">股东名称</td>
        <td colspan="5">
          <app-tdcoy
            class="acNameWrap"
            :key-no="keyNo"
            :name="name"
            :image="viewData.bodyImageUrl"
            :hideImage="hideImage"
          ></app-tdcoy>
        </td>
      </tr>
      <tr>
        <td class="tb" width="20%">{{ nameLabel }} </td>
        <td colspan="5">
          <app-coy
            v-if="type === 'control' && viewData.mutiActual"
            :coy-arr="viewData.Name"
          ></app-coy>
          <app-tdcoy
            v-else
            class="acNameWrap"
            :key-no="viewData.KeyNo"
            :name="viewData.Name"
            :image="viewData.ActualControllerImageUrl || viewData.ImageUrl"
            :hideImage="hideImage"
          ></app-tdcoy>
        </td>
      </tr>
      <tr v-if="type !== 'control'">
        <td class="tb" width="20%">{{ viewData.PercentTotalLabel || "总持股比例" }}</td>
        <td width="20%">
          {{ viewData.PercentTotal || "-" }}
        </td>
        <td class="tb" width="15%">{{ viewData.StockPercentLabel || "直接持股比例" }}</td>
        <td width="15%">
          {{ viewData.StockPercent || "-" }}
        </td>
        <td class="tb" width="15%">{{ viewData.IndirectStockPercentLabel || "间接持股比例" }}</td>
        <td width="15%">
          {{ viewData.IndirectStockPercent || "-" }}
        </td>
      </tr>
      <tr v-else-if="type === 'control' && viewData.PercentTotal">
        <td class="tb" width="20%">{{ totalPercentLabel }}</td>
        <td colspan="3">
          {{ viewData.PercentTotal || "-" }}
        </td>
      </tr>
      <tr v-if="isPathChainCustom">
        <td class="tb" width="20%">直接持股</td>
        <td colspan="5">
          {{ viewData.DirectPercent || "-" }}
        </td>
      </tr>
      <tr v-if="viewData.showControllChart">
        <td class="tb" width="20%">
          控制图谱
        </td>
        <td colspan="5" style="padding:0px">
          <div style="position: relative; height: 450px">
            <actual-controller-charts
              ref="actualcontroller"
              isOutData
              noDataStyle
              needFuns
              :aKeyNo="keyNo"
              iframe
              :aName="name"
              typeMini
              lazy-init
            ></actual-controller-charts>
          </div>
        </td>
      </tr>
      <tr>
        <td class="tb" width="20%">
          {{ pathLabel }}
          <app-glossary-info
            class="glossary-style"
            v-if="pathTsId"
            :info-id="pathTsId"
            placement="bottom-start"
          ></app-glossary-info>
        </td>
        <td colspan="5">
          <app-tdpath
            :name="name"
            :key-no="keyNo"
            :is-control="isControl"
            :reverse="reverse"
            :is-invest="isInvest"
            :paths="viewData.Paths"
            :list="viewData.pathList"
          ></app-tdpath>
        </td>
      </tr>
    </table>
  </app-popup>
</template>

<script>
import actualControllerCharts from '../../../../../charts/actualcontroller-charts/index-account.vue'
export default {
  name: 'AppInvestDetail',
  components: { actualControllerCharts },
  data() {
    return {
      tableHeight: 200,
      name: '',
      keyNo: '',
      visible: false,
      params: {},
      viewData: null,
      nameLabel: '间接持股企业名称',
      totalPercentLabel: '间接持股比例',
      pathLabel: '投资链',
      isPathChainCustom: false,
      title: '投资链',
      stockName: '',
      pathTsId: '', // 注释id
      type: '', // 链的类型
      isControl: false,
      isInvest: true,
      reverse: false,
      hideImage: false
    }
  },
  watch: {},
  mounted() {
    this.show({ name: this.name, keyNo: this.keyNo, viewData: this.viewData })
  },
  methods: {
    show({ name, keyNo, viewData }) {
      this.keyNo = keyNo
      this.name = name
      this.viewData = viewData
      this.visible = true
      this.nameLabel = viewData.nameLabel || '间接持股企业名称'
      this.totalPercentLabel = viewData.totalPercentLabel || '间接持股比例'
      this.pathLabel = viewData.pathLabel || '投资链'
      this.isPathChainCustom = viewData.isPathChainCustom || false
      this.title = viewData.title || '投资链'
      this.stockName = viewData.stockName || ''
      this.pathTsId = viewData.pathTsId || ''
      this.type = viewData.type || ''
      if (this.type === 'control') {
        this.isControl = true
        this.reverse = true
        this.isInvest = false
      } else if (this.type === 'shareholding') {
        this.isControl = false
        this.reverse = true
        this.isInvest = false
      }

      setTimeout(() => {
        if (this.$refs.actualcontroller) {
          this.$refs.actualcontroller.onRefreshNew(this.viewData.chartData, this.viewData.source)
        }
      }, 100)
    }
  }
}
</script>
<style lang="scss" scoped>
.glossary-style {
  top: 0;
  ::v-deep {
    .iconfont {
      font-size: 14px;
      color: #d8d8d8;
      font-weight: normal;
      vertical-align: middle;
    }
  }
}

.acNameWrap {
  ::v-deep {
    .td-name {
      margin: 0 !important;
    }
  }
}

.invest-detail {
  ::v-deep {
    .td-path-list {
      .company-name {
        em {
          color: #333;
        }
      }
    }
  }
}
</style>
