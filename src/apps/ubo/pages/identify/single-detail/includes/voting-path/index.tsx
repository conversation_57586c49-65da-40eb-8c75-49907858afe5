import QModal from '@/components/global/q-modal';
import QPlainTable from '@/components/global/q-plain-table';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { company } from '@/shared/services';
import { defineComponent, onMounted, ref } from 'vue';

const VotingPath = defineComponent({
  name: 'VotingPath',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const fetchData = async () => {
      if (!props.params.keyNo) return;
      const res = await company.getVotingPath(props.params.keyNo);
      return res;
    };

    const handleCancel = () => {
      emit('resolve', null);
    };

    onMounted(() => {
      fetchData();
    });

    return {
      handleCancel,
    };
  },
  render() {
    return (
      <QModal visible={true} title="控制链" footer={false} onCancel={this.handleCancel}>
        <QPlainTable>
          <tr>
            <td class="tb">实际控制人</td>
            <td></td>
          </tr>
          <tr>
            <td class="tb">表决权比例</td>
            <td></td>
          </tr>
          <tr>
            <td class="tb">控制图谱</td>
            <td></td>
          </tr>
          <tr>
            <td class="tb">控制链</td>
            <td></td>
          </tr>
        </QPlainTable>
      </QModal>
    );
  },
});

export default VotingPath;

export const openVotingPath = createPromiseDialog(VotingPath);
