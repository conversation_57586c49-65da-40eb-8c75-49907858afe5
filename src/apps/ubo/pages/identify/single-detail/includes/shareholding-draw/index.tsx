import { createPromiseDialog } from '@/components/promise-dialogs';
import EquityChain from '@/shared/charts/equity-chain';
import { Drawer } from 'ant-design-vue';
import { defineComponent, ref } from 'vue';

const ShareholdingDraw = defineComponent({
  name: 'ShareholdingDraw',
  props: {},
  setup() {
    const visible = ref(true);
    return {
      visible,
    };
  },
  render() {
    return (
      <Drawer
        visible={this.visible}
        width={'78%'}
        title="股权穿透图谱"
        onClose={() => {
          this.visible = false;
        }}
      >
        <div style={{ height: '100%' }}>
          <EquityChain />
        </div>
      </Drawer>
    );
  },
});

export default ShareholdingDraw;

export const openShareholdingDraw = createPromiseDialog(ShareholdingDraw);
