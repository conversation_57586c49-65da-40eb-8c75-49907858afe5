import { type RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { Permission } from '@/config/permissions.config';
import { APP_MENU_CONFIG } from '@/config/menu.config';

export const uboRoutes = (): RouteConfig[] => [
  {
    path: '/ubo',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '受益人识别',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '受益人识别',
    },
    redirect: '/ubo/identify',
    children: [
      {
        path: '/ubo/identify',
        redirect: () => {
          // FIXME: 通过权限判断跳转链接
          return {
            name: 'ubo-identify-single',
          };
        },
      },
      /**
       * 单一识别
       */
      {
        path: 'identify/single',
        name: 'ubo-identify-single',
        component: () => import('../pages/identify/single'),
        meta: {
          title: '单一识别',
          permission: [Permission.INVESTIGATION_VIEW], // FIXME: 使用新权限
        },
      },
      {
        path: 'identify/single/:id([a-z0-9]+)',
        name: 'ubo-identify-single-detail',
        component: () => import('../pages/identify/single-detail'),
        props: true,
        meta: {
          title: '单一识别详情',
          permission: [Permission.INVESTIGATION_VIEW], // FIXME: 使用新权限
        },
      },

      /**
       * 批量识别
       */
      {
        path: 'identify/batch',
        name: 'ubo-identify-batch',
        component: () => import('../pages/identify/batch'),
        meta: {
          title: '批量识别',
          permission: [Permission.INVESTIGATION_VIEW], // FIXME: 使用新权限
        },
      },
      {
        path: 'identify/batch/:id([0-9]+)',
        name: 'ubo-identify-batch-detail',
        component: () => import('../pages/identify/batch-detail'),
        props: true,
        meta: {
          title: '批量识别详情',
          permission: [Permission.INVESTIGATION_VIEW], // FIXME: 使用新权限
        },
      },

      /**
       * 识别列表
       */
      {
        path: 'identify/history',
        name: 'ubo-identify-history',
        component: () => import('../pages/identify/history'),
        meta: {
          title: '识别列表',
          permission: [Permission.INVESTIGATION_VIEW], // FIXME: 使用新权限
        },
      },
      {
        path: 'identify/history/:id([0-9]+)',
        name: 'ubo-identify-history-detail',
        component: () => import('../pages/identify/history-detail'),
        props: true,
        meta: {
          title: '识别列表详情',
          permission: [Permission.INVESTIGATION_VIEW], // FIXME: 使用新权限
        },
      },
    ],
  },
];
