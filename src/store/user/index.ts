import { ActionTree, GetterTree, MutationTree } from 'vuex';
import _ from 'lodash';

import { user as userService } from '@/shared/services';
import { changeFavicon } from '@/utils';
import { setUser } from '@/utils/sentry';

import { IRootState, IUserState, IPermissionCategory } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<IUserState> = {
  SET_PROFILE(state, payload) {
    state.profile = payload;
  },
  SET_USAGE(state, payload) {
    state.usage = payload;
  },
  SET_BUNDLE(state, payload) {
    state.bundle = payload;
  },

  SET_WHOLE_PERMISSIONS(state, payload) {
    state.wholePermissions = payload;
  },
  SET_ORGANIZATIONS(state, payload) {
    state.organizations = payload;
  },
  SWITCH_ORGANIZATION(state, currentOrg) {
    if (!state.profile) {
      state.profile = {};
    }
    (state.profile as any).currentOrg = currentOrg;
  },
  SET_DIMENSIONMAP(state, payload) {
    state.riskDimensionMap = payload.dimensions;
    state.tenderDimensionMap = payload.tenderDimensions;
  },
  SET_PERSON_LIST(state, payload) {
    state.personList = payload;
  },
};

export const actions: ActionTree<IUserState, IRootState> = {
  async getProfile({ commit, getters }) {
    try {
      const profile = await userService.getProfile();
      if (profile instanceof Error) {
        throw new Error('获取 profile 失败');
      }

      // Sentry tracking user
      setUser({
        ..._.pick(profile, ['id', 'guid', 'username', 'nickname', 'organizationId', 'bundle']),
      });

      commit('SET_PROFILE', profile);
      return Promise.resolve(profile);
    } catch (err) {
      commit('SET_PROFILE', null);
      throw err;
    }
  },
  async getBundle({ commit }) {
    try {
      const bundle = await userService.roverBundle();
      commit('SET_BUNDLE', bundle);
    } catch (err) {
      commit('SET_BUNDLE', null);
      throw err;
    }
  },
  async getUsage({ commit }) {
    try {
      const usage = (await userService.roverUsage()) as any;
      // 部门套餐
      const departmentBundleUsage = usage.bundleUsage;
      // 个人用量
      const bundleUsage = {
        ...(usage?.bundleUsage || {}),
        ...(usage?.mainBundleUsage?.usage || {}),
      };
      commit('SET_USAGE', {
        ...usage,
        bundleUsage,
        departmentBundleUsage,
      });
    } catch (err) {
      commit('SET_USAGE', null);
      throw err;
    }
  },

  // 获取权限列表
  async fetchPermissions({ commit }): Promise<any> {
    const permissions: any = await userService.getPermissions();
    permissions.map((item: IPermissionCategory) => {
      if (!item.tabs) {
        item.tabs = [
          {
            title: '',
            permissions: item.permissions,
          },
        ];
        delete item.permissions;
      }
      item.tabs = item.tabs.map((it) => ({
        ...it,
        permissions: it.permissions?.map((permission) => ({
          ...permission,
          label: permission.name,
          value: permission.permission,
        })),
      }));

      return item;
    });
    commit('SET_WHOLE_PERMISSIONS', permissions);
  },
  // 获取组织列表
  async getOrganizations({ commit }) {
    try {
      const orgs = await userService.getOrgs();
      commit('SET_ORGANIZATIONS', orgs);
      return orgs;
    } catch (err) {
      commit('SET_ORGANIZATIONS', []);
      throw err;
    }
  },
  async updateOrganization({ commit, state }, payload) {
    const orgList = (state as any).organizations.slice();
    const org = await userService.updateOrganizationInfo(payload);
    const index = _.findIndex(orgList, { organizationId: org.organizationId });
    orgList.splice(index, 1, org);
    // 更新组织列表
    commit('SET_ORGANIZATIONS', orgList);
    return org;
  },
  /**
   * 切换当前组织
   */
  async switchOrganization({ commit, state }, currentOrgId) {
    if (!currentOrgId) {
      return Promise.reject(new Error('要切换的组织ID不能为空'));
    }

    if (state.profile === null) {
      return Promise.reject(new Error('用户未登录'));
    }

    return userService.switchOrganization(currentOrgId, (state.profile as any)?.userId).then((r) => {
      commit('SWITCH_ORGANIZATION', currentOrgId);
      return r;
    });
  },

  async getDimensionData({ commit }) {
    try {
      const [dimensions, tenderDimensions] = await Promise.all([userService.getDimensionList(), userService.getTenderDimensionList()]);
      commit('SET_DIMENSIONMAP', { dimensions, tenderDimensions });
    } catch (err) {
      commit('SET_DIMENSIONMAP', {});
      throw err;
    }
  },
  async fetchPersonList({ commit, state }) {
    if (state.personList.length) {
      return;
    }
    const res = await userService.getUserList();
    commit('SET_PERSON_LIST', res);
  },
};

export const getters: GetterTree<IUserState, IRootState> = {
  profile(state) {
    return state.profile;
  },
  wholePermissions(state) {
    return state.wholePermissions;
  },
  organizations(state) {
    return state.organizations;
  },
  currentOrg(state: any) {
    return _.find(state.organizations, { organizationId: state.profile?.currentOrg });
  },
  usage(state) {
    return state.usage;
  },
  permissions(state) {
    return state?.profile?.permissions || [];
  },
  isZeissOrg(state) {
    return state?.profile?.groupVersion === 'v2'; // NOTE: 使用 groupVersion 判断是否为 zeiss
  },
  getDimension(state) {
    return state?.riskDimensionMap;
  },
  getTenderDimension(state) {
    return state?.tenderDimensionMap;
  },
  getBundles(state) {
    return state?.profile?.bundle || {};
  },
  isOwner(state) {
    return state?.profile?.isOwner;
  },
  personList(state) {
    return state?.personList;
  },
};

export const state: IUserState = {
  profile: null,
  bundle: null,
  usage: null,
  wholePermissions: [],
  organizations: [],
  riskDimensionMap: {},
  tenderDimensionMap: {},
  personList: [],
};
