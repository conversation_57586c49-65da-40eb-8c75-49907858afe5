import { defineComponent, nextTick, onMounted, PropType, reactive, ref } from 'vue';

import { NodeType } from '@/utils/yfiles/lib/node-type';
import { handleHoverClass } from '@/utils/yfiles/lib/NodeHighlightManager';
import { getNodeSize } from '@/utils/yfiles/templates/helper';
import { buildGraph, createLayoutConfig, createLayoutData } from './graph';
import '@/utils/yfiles/license-loader/license-loader';
import { Arrow, ArrowType, GraphItemTypes, Insets, ScrollBarVisibility, Stroke } from 'yfiles';
import { startsWith } from 'lodash';
import { loadUboStructureData } from './data';
import { isFakeKeyNo } from '@/utils';
import ImgLogo from '@/assets/images/tip-logo.png';
import styles from './equity-chain.module.less';
import { Slider } from 'ant-design-vue';
import QIcon from '@/components/global/q-icon';
import { useFullscreen } from '@vueuse/core';
import { usePopover } from './utils/popover';
import { AppCompany, AppPerson } from '@/shared/components/info-pop';
import { useSaveImg } from './utils/save-img';

export const LineType = {
  solid: 'solid',
  dashed: 'dashed',
};

export const createArrowStyle = ({
  /** 边框颜色 */
  strokeColor = '#128BED',
  /** 填充色 */
  fillColor = '#128BED',
} = {}) => {
  return new Arrow({
    type: ArrowType.TRIANGLE,
    stroke: strokeColor,
    fill: fillColor,
  });
};

const EquityChain = defineComponent({
  name: 'EquityChain',
  props: {
    containerId: {
      type: String,
      default: 'equity-chain',
    },
    companyInfo: {
      type: Object as PropType<{ name: string; keyNo: string }>,
      default: () => ({ name: '企查查科技股份有限公司', keyNo: 'f625a5b661058ba5082ca508f99ffe1b' }),
    },
    // 传入该值时，将会过滤列表，将指定的内容才会显示在图上，参考样例： ['雷军', '小米科技有限公司']
    nodeKeyNoList: {
      type: Array as PropType<string[]>,
      required: false,
    },
  },
  setup(props, { emit }) {
    const DEFAULT_SCALE = 100;
    const containerRef = ref<HTMLElement | null>(null);

    const graphInstance = ref();
    const dataSource = ref<{ nodes: any; links: any }>({ links: [], nodes: [] });
    const scale = ref<number>(DEFAULT_SCALE);
    const maxScale = ref<number>(250);
    const minScale = ref<number>(40);

    const chartData = reactive<{ nodes: any; edges: any }>({
      nodes: [],
      edges: [],
    });

    /**
     * 全屏显示控制
     */
    const { isFullscreen, toggle } = useFullscreen(containerRef);

    const { saveImage } = useSaveImg(graphInstance, props.companyInfo.name);

    const { load: showPopover, move: movePopover, unload: hidePopover } = usePopover(containerRef);

    const clearHover = () => {
      hidePopover();
    };

    const handleNodeHover = ({ event }) => {
      const hoverItem = event.item;

      if (!hoverItem) {
        clearHover();
      }

      /** 置灰的节点不展示popover */
      if (hoverItem?.style?.cssClass === 'gray20') {
        return;
      }

      const data = dataSource.value.nodes.find((item) => item.keyNo === hoverItem?.tag?.unique);

      if (!data) {
        clearHover();
        return;
      }

      // this.hoverData = data

      if (!data?.keyNo || data.org < 0) {
        // -1 无效企业 -2无效的人
        /** combined类型，全部存在keyNo，不展示复制 */
        let combinedAllNoKey = false;
        if (hoverItem?.tag?.combinedNameList?.length) {
          const noKeyNo = hoverItem?.tag?.combinedNameList?.find((i) => !i.keyNo);
          combinedAllNoKey = !noKeyNo;
        }
        if (combinedAllNoKey) {
          clearHover();
          return;
        }

        // popoverHelper.showDetailCard({
        //   component: appCopy,
        //   data: {
        //     text: data.name,
        //     isVertical: false,
        //   },
        //   direction: 'bottom',
        //   directionX: 'left',
        //   size,
        //   container: $(`#${this.containerId}`),
        //   identity: `detail-popover-${data.nodeId}`,
        //   callback: this.moveCardPosition(event.item, 'copy'),
        //   isYFile: true,
        // });

        return;
      }

      if (isFakeKeyNo(data.keyNo)) {
        return;
      }

      const popoverComp = data.keyNo && data.keyNo[0] === 'p' ? AppPerson : AppCompany;
      showPopover(popoverComp, {
        props: {
          id: data.keyNo,
        },
        data: {
          // id: data.keyNo,
          hasKeyNo: true,
          name: data.name,
          keyNo: data.keyNo,
          rsTags: data.rsTags,
          eid: props.companyInfo.keyNo,
          ename: props.companyInfo.name,
          org: data.org,
          hideEmptyDataInPath: true,
        },
      });
      movePopover({
        nodeItem: hoverItem,
        graphComponent: graphInstance.value,
      });

      // popoverHelper.showDetailCard({
      //   component: data.keyNo && data.keyNo[0] === 'p' ? appPerson : appCompany,
      //   data: {
      //     hasKeyNo: true,
      //     id: data.keyNo,
      //     name: data.name,
      //     keyNo: data.keyNo,
      //     rsTags: data.rsTags,
      //     eid: this.companyKeyNo,
      //     ename: this.companyName,
      //     org: data.org,
      //     hideEmptyDataInPath: true,
      //   },
      //   container: $(`#${this.containerId}`).parent(),
      //   size,
      //   identity: `detail-${data.nodeId}`,
      //   callback: this.moveCardPosition(event.item, 'card'),
      //   isYFile: true,
      // });
    };

    const zoomIn = () => {
      const newZoom = graphInstance.value.zoom + 0.2;
      graphInstance.value.zoom = Math.max(newZoom, 0.2);
    };
    const zoomOut = () => {
      graphInstance.value.zoom -= 0.2;
    };
    const zoomScale = (scale) => {
      if (scale && graphInstance.value?.zoom) {
        graphInstance.value.zoom = scale / 100;
      }
    };

    const supportsCssClass = (style) => {
      return 'cssClass' in style && typeof style.cssClass === 'string';
    };

    const clearHighlights = (graphComponent) => {
      graphComponent.graph.nodes.forEach((node) => {
        if (supportsCssClass(node.style)) {
          node.style.cssClass = '';
        }
      });

      graphComponent.graph.edges.forEach((edge) => {
        const edgeStyle = edge.style.clone();
        if (edge.tag.isMain) {
          edgeStyle.stroke = new Stroke({
            fill: '#FF6060',
            thickness: 1.5,
            dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
          });
          edgeStyle.targetArrow = createArrowStyle({
            strokeColor: '#FF6060',
            fillColor: '#FF6060',
          });
        } else {
          edgeStyle.stroke = new Stroke({
            fill: '#E3E3E3',
            thickness: 1.5,
            dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
          });
          edgeStyle.targetArrow = createArrowStyle();
        }
        graphComponent.graph.setStyle(edge, edgeStyle);

        if (supportsCssClass(edge.style)) {
          edge.style.cssClass = '';
        }
      });

      graphComponent.graph.labels.forEach((label) => {
        if (supportsCssClass(label.style)) {
          label.style.cssClass = '';
        }
      });
    };

    const reset = () => {
      scale.value = DEFAULT_SCALE;
      hidePopover();
      graphInstance.value.morphLayout({
        targetBoundsInsets: Insets.from([0, 0, 0, 0]),
        layout: createLayoutConfig(),
        layoutData: createLayoutData(graphInstance.value),
      });
      clearHighlights(graphInstance.value);
    };

    const initializeGraphComponent = async () => {
      const { graphComponent, graphBuilder, nodeSource, edgeSource, graphInputMode } = buildGraph(`${props.containerId}`);

      graphComponent.maximumZoom = maxScale.value / 100;
      graphComponent.minimumZoom = minScale.value / 100;

      graphComponent.focusIndicatorManager.enabled = false;
      graphComponent.selectionIndicatorManager.enabled = false;

      chartData.nodes = [];
      chartData.edges = [];
      // 数据处理
      dataSource.value.nodes.forEach((node) => {
        const combinedHeight = 0;

        const areaTag = node.tags?.length
          ? {
              name: node.tags[0].name,
            }
          : { name: '' };
        const nodeType = node.listedAc?.length > 1 ? NodeType.combined : startsWith(node.keyNo, 'p') ? NodeType.person : NodeType.company;
        const { isOversize, layoutSize, nodeSize, labelSize, tagSize } = getNodeSize({
          nodeType,
          text: node.name,
          labels: node.rsTags,
          tag: areaTag.name,
          fixedHeight: combinedHeight,
          isBold: node.isRoot,
        });

        const preparedNode = {
          id: node.nodeId,
          isTopNode: node.isTopNode,
          name: node.name,
          unique: node.keyNo,
          isSelf: node.isRoot,
          type: nodeType,
          areaTag,
          tipLabels: node?.rsTags,
          imageUrl: node.image ? node.image : '',
          level: node.level,
          isOversize,
          combinedHeight,
          layoutSize,
          nodeSize,
          labelSize,
          tagSize,
        };

        chartData.nodes.push(preparedNode);
      });

      dataSource.value.links.forEach((link) => {
        chartData.edges.push({
          id: link.linkId,
          sourceId: link.source,
          targetId: link.target,
          lineType: link.lineType,
          lineText: link.lineText,
          isMain: link.isRed,
          tips: link.tips,
        });
      });
      graphBuilder.setData(nodeSource, chartData.nodes);
      graphBuilder.setData(edgeSource, chartData.edges);

      graphComponent.fitGraphBounds();

      graphComponent.addZoomChangedListener(() => {
        const value = Number(graphComponent.zoom?.toFixed(2));
        scale.value = Number((value * 10).toFixed(0)) * 10;
      });

      graphComponent.verticalScrollBarPolicy = ScrollBarVisibility.NEVER;
      graphComponent.horizontalScrollBarPolicy = ScrollBarVisibility.NEVER;
      graphComponent.addMouseDragListener((_sender, evt) => {
        clearHover();
      });

      graphBuilder.updateGraph();

      graphComponent.fitGraphBounds();
      // 更新布局
      await graphComponent.morphLayout({
        targetBoundsInsets: Insets.from([0, 0, 0, 0]),
        layout: createLayoutConfig(),
        layoutData: createLayoutData(graphComponent),
        morphDuration: '0.3s',
      });
      graphInputMode.addItemClickedListener((_sender, evt) => {
        const tag = evt.item.tag;
        if (tag?.unique) {
          const entity = {
            id: tag.unique,
            eid: tag.unique,
            ename: tag.name,
            org: '',
            pathData: null,
          };
          emit('showDetail', { data: entity });
        }
      });

      graphInputMode.addCanvasClickedListener((_sender, evt) => {
        emit('clickEmpty');
      });

      graphInputMode.itemHoverInputMode.hoverItems = GraphItemTypes.NODE;
      // 定义一个变量来保存定时器的ID
      let timerId: number | null = null;
      graphInputMode.itemHoverInputMode.addHoveredItemChangedListener((_sender, evt) => {
        /** 处理hover效果 */
        handleHoverClass(evt, graphInstance.value, (row) => {
          if (row.type === 'person' && row.isTopNode) {
            return 'top-node ';
          } else if (row.type === 'company' && row.isTopNode) {
            return 'top-node ';
          }
          return '';
        });

        if (evt.item) {
          // 如果鼠标悬停在节点上，则设置一个定时器，在500毫秒后触发
          timerId = setTimeout(() => {
            handleNodeHover({ event: evt });
            timerId && clearTimeout(timerId); // 清除定时器
            timerId = null; // 重置定时器ID
          }, 200);
        } else {
          // 如果鼠标离开节点，则清除定时器
          timerId && clearTimeout(timerId);
          timerId = null;
          clearHover();
        }
      });

      graphInstance.value = graphComponent;
    };

    const initData = () => {
      return new Promise((resolve, reject) => {
        // this.isInit = false
        // this.noData = false
        loadUboStructureData(
          {
            keyNo: props.companyInfo.keyNo,
            name: props.companyInfo.name,
            // percent: this.percent,
            // mode: this.options.mode || 0,
            // from: this.options.from || '',
            // fromCorpKeyNo: this.options.fromCorpKeyNo || '',
            // functionTableId: this.options.functionTableId || '',
          },
          props.nodeKeyNoList
          // this.$route.name === 'breakthrough-structure-chart'
        )
          .then((data) => {
            // this.isLoading = false;
            // this.data = data
            dataSource.value = data;
            emit('loadDataSuccess', {
              companyName: props.companyInfo.name,
              companyKeyNo: props.companyInfo.keyNo,
            });
            resolve({});
          })
          .catch((err) => {
            // this.isInit = true;
            // this.noData = true;
            reject(err);
          });
      });
    };

    onMounted(async () => {
      await initData();
      initializeGraphComponent();
    });

    return {
      zoomIn,
      zoomOut,
      zoomScale,
      scale,
      maxScale,
      minScale,
      reset,
      saveImage,
      containerRef,
      isFullscreen,
      toggle,
    };
  },
  render() {
    return (
      <div class={styles.container} ref="containerRef">
        {this.$slots.default}
        <div class={styles.content}>
          {/* 图谱 */}
          <div id={this.containerId} class={styles.chart}></div>
          {/* 工具栏 */}
          <div class={styles.toolbox}>
            {/* 缩放 */}
            <Slider
              class={styles.slider}
              vertical
              v-model={this.scale}
              max={this.maxScale}
              min={this.minScale}
              onAfterChange={this.zoomScale}
            />
            <ul class={styles.btnGroup}>
              {/* 刷新 */}
              <li class={styles.btnItem} onClick={this.reset}>
                <i class={styles.icon}>
                  <QIcon type="icon-icon_sqq" />
                </i>
                <span class={styles.label}>刷新</span>
              </li>
              {/* 全屏 */}
              <li class={styles.btnItem} onClick={this.toggle}>
                <i class={styles.icon}>
                  <QIcon type={this.isFullscreen ? 'icon-tuichu' : 'icon-quanping1'} />
                </i>
                <span class={styles.label}>{this.isFullscreen ? '退出' : '全屏'}</span>
              </li>
              {/* 保存图片 */}
              <li class={styles.btnItem} onClick={this.saveImage}>
                <i class={styles.icon}>
                  <QIcon type="icon-xiazai" />
                </i>
                <span class={styles.label}>保存</span>
              </li>
            </ul>
          </div>
        </div>
        {/* 免责声明 */}
        <div class={styles.tips}>
          以上数据是
          <img src={ImgLogo} alt="logo" width={55} />
          大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。
        </div>
      </div>
    );
  },
});

export default EquityChain;
