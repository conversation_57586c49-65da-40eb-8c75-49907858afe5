import { PropType, defineComponent } from 'vue';
import { uniqBy } from 'lodash';

import Icon from '@/shared/components/icon';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { openBatchPasteDialog } from '@/components/modal/bach-paste-dialog';

import styles from './text-upload.module.less';

type CompanyInfo = {
  companyId: string;
  companyName: string;
  KeyNo: string;
  Name: string;
  ShortStatus?: string;
  ActualName?: string;
  ImageUrl?: string;
  IsHide?: number;
};

const TextUpload = defineComponent({
  name: 'TextUpload',
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    hint: {
      type: String,
      required: false,
    },
    icon: {
      type: String,
      default: 'icon-wenbenniantie',
    },
    height: {
      type: String,
      default: 'auto',
    },
    theme: {
      type: String as PropType<'default' | 'lighter'>,
      default: 'default',
    },
    validate: {
      type: Function as PropType<(data: CompanyInfo[]) => boolean>,
      required: false,
    },
  },
  setup(props, { emit }) {
    // const router = useRouter();
    const track = useTrack();

    // const ability = useAbility();

    // const abilityCheck = inject('abilityCheck', async (args) => {
    //   return ability?.check('stock', args);
    // }) as (features?: string[]) => Promise<boolean>;
    const showTextImportModal = async () => {
      // if (!(await abilityCheck())) {
      //   return;
      // }
      track(createTrackEvent(6208, '批量排查', '输入文本'));
      const res = await openBatchPasteDialog({
        tabs: [
          {
            key: 'paste',
            label: '输入文本',
          },
        ],
        validate: props.validate,
      });
      if (!res) {
        return;
      }
      try {
        // 在匹配时就排除了境外企业
        const data = uniqBy(res, 'KeyNo');
        const params = data.map((v: any) => {
          return {
            companyName: v.Name,
            companyId: v.KeyNo,
          };
        });
        // const ids = data.map(({ KeyNo }: any) => KeyNo);
        // const names = data.map(({ Name }: any) => Name);

        // FIXME: 不超过5个跳转到详情
        // if (params.length <= 5) {
        //   track(createTrackEvent(6208, '批量排查', '输入文本不超过5家'));
        //   router?.push({
        //     name: 'supplier-investigate-detail',
        //     params: {
        //       type: 'batch-investigation',
        //       id: ids[0],
        //     },
        //     query: {
        //       ids: ids.join(','),
        //       names: names.join(','),
        //       from: 'assessment',
        //     },
        //   });
        //   return;
        // }
        // await batchImport.batchImport('keyNo', { data: params });
        emit('success', params);
      } catch (error) {
        console.error(error);
      }
    };

    return {
      showTextImportModal,
    };
  },
  render() {
    const placeholder = this.$slots.placeholder || this.placeholder;
    return (
      <div
        class={[styles.container, styles[this.theme]]}
        style={{ height: this.height }}
        data-testid="text-input"
        onClick={async () => {
          await this.showTextImportModal();
        }}
      >
        <div class={styles.icon}>
          <Icon type={this.icon} />
        </div>
        <div class={styles.placeholder}>
          <p v-show={placeholder} class={styles.text}>
            {placeholder}
          </p>
          <p v-show={this.hint} class={styles.hint}>
            {this.hint}
          </p>
        </div>
      </div>
    );
  },
});

export default TextUpload;
