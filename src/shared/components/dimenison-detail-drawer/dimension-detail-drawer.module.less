.container {
  .body {
    padding: 10px 15px;
  }

  .title {
    font-size: 15px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    color: #333;
  }

  .subTitle {
    position: relative;
    padding-left: 13px;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    line-height: 32px;
    color: #333;
    margin: 16px 0 10px;

    &::before {
      content: '';
      position: absolute;
      width: 4px;
      height: 16px;
      border-radius: 2px;
      background: #128bed;
      top: 50%;
      transform: translateY(-50%);
      left: 2px;
    }
  }
}
