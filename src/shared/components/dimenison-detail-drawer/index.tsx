import { computed, defineComponent, onMounted, ref } from 'vue';
import { Drawer, Spin } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';

import styles from './dimension-detail-drawer.module.less';
import { ChangeTypeMap, StandardMap } from '@/apps/risk-monitor/pages/trends/config/search-config';
import ChangeTable from './widgets/change-table';
import QEntityLink from '@/components/global/q-entity-link';

const SubTitleMap = {
  1: '新增',
  2: '减少',
  3: '受益所有人信息变更',
};

const ChangeReasonMap = {
  1: '持股',
  2: '表决权',
  3: '任职类型变更',
};

const ChangeTrendsMap = {
  1: '上升',
  2: '下降',
  3: '',
};

const TargetsTrends = defineComponent({
  name: 'TargetsTrends',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const visible = ref(false);
    const init = ref(false);
    const recordData = computed(() => props.params?.data || {});

    onMounted(async () => {
      visible.value = true;
      init.value = true;
    });

    return {
      init,
      visible,
      recordData,
    };
  },
  render() {
    return (
      <Drawer
        wrapClassName={styles.container}
        visible={this.visible}
        footer={false}
        width={'90%'}
        closable={false}
        onClose={() => {
          this.visible = false;
        }}
      >
        <div slot="title" class="drawer-title">
          <div>
            <q-entity-link coyObj={{ Name: this.recordData.companyName, KeyNo: this.recordData.companyId }} />-
            {ChangeTypeMap[this.recordData.changeType]}
          </div>
          <q-icon
            class="icon-close"
            type="icon-tanchuangguanbi"
            onClick={() => {
              this.visible = false;
            }}
          ></q-icon>
        </div>
        <div class={styles.body}>
          <div class={styles.title}>
            {StandardMap[this.recordData.standard]}：{ChangeTypeMap[this.recordData.changeType]}
          </div>
          {this.recordData.changeContent?.dynamicBoiChangeContent?.map((item) => {
            return (
              <div>
                <div class={styles.subTitle}>
                  {SubTitleMap[this.recordData.changeType]}：
                  <QEntityLink coyObj={{ KeyNo: item.perKeyNo, Name: item.perName }} />
                  ，变更原因：
                  {ChangeReasonMap[this.recordData.standard]}
                  {ChangeTrendsMap[this.recordData.changeType]}
                </div>
                <ChangeTable
                  standard={this.recordData.standard}
                  changeType={this.recordData.changeType}
                  before={item?.beforeValue}
                  after={item?.afterValue}
                />
              </div>
            );
          })}
        </div>
      </Drawer>
    );
  },
});
export const openDimensionDetailDrawer = createPromiseDialog(TargetsTrends);
