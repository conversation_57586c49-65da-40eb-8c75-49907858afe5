/* eslint-disable camelcase */
export enum BatchBusinessTypeEnum {
  UBO = 1,
  // TODO 以下待删除
  /**
   * 任务-批量排查-excel
   */
  Diligence_File = 10, // 批量排查-excel上传 2011
  /**
   * 任务-批量排查-输入框手动填写
   */
  Diligence_ID = 11, // 批量排查-输入框手动填写 2011
  /**
   * 任务-批量排查-从合作伙伴中选择
   */
  Diligence_Customer = 12, // 批量排查-从合作伙伴中选择 2012
  /**
   * 任务-全量风险排查年检
   */
  Diligence_Customer_Analyze = 13, // 批量排查-全部客商-全量风险排查年检 2012
  // Diligence_Continuous = 14, // 批量-持续排查 已废弃
  /**
   * 导入-第三方列表-excel
   */
  Customer_File = 20, // 合作伙伴-excel上传 2032
  Customer_ID = 21, // 合作伙伴-文本输入 2032
  /**
   * 导入-人员列表-excel
   */
  Person_File = 22, // 人员-excel上传 2062
  /**
   * 导入-内部黑名单列表-excel
   */
  InnerBlacklist_File = 23, // 内部黑名单-excel上传 2042
  /**
   * 任务-批量招标排查企业-excel
   */
  Bidding_Diligence_File = 24, // 批量招标排查-excel上传
  /**
   * 任务-批量特定利益关系企业排查-excel
   */
  Interest_Investigation_File = 50, // 批量特定利益关系企业排查-excel上传
  /**
   * 导入-合作监控企业-excel
   */
  Monitor_File = 25, // 合作监控-excel上传
  /**
   * 导出-批量排查-公司列表概览
   */
  Diligence_Batch_Detail = 30, // 排查结果 导出 2011
  /**
   * 导出-排查记录列表
   */
  Diligence_Record = 31, // 排查记录 导出 2022
  /**
   * 导出-人员列表
   */
  Person_Export = 32, // 人员导出 2061
  /**
   * 导出-内部黑名单列表
   */
  InnerBlacklist_Export = 33, // 内部黑名单导出 2041
  /**
   * 导出-排查报告pdf-单个
   */
  Diligence_Report_Export = 34, // 排查报告导出 pdf 2002
  /**
   * 导出-第三方列表
   */
  Customer_Export = 35, // 第三方列表导出 2031
  /**
   * 导出-批量排查-维度详情
   */
  Dimension_Detail_Export = 36, // 维度详情导出
  /**
   * 导出-招投标详情页pdf
   */
  Tender_Detail_Export = 37, // 标讯详情pdf导出
  /**
   * 导出-招投标列表
   */
  Tender_Export = 38, // 标讯导出
  /**
   * 导出-风险动态列表
   */
  Risk_Export = 39, // 风险动态导出
  /**
   * 导出-舆情动态列表
   */
  Sentiment_Export = 40, // 舆情动态导出
  /**
   * 导出-排查报告pdf-批量压缩包
   */
  Diligence_Report_Batch_Export = 41, // 批量排查报告 批量导出
  /**
   * 导出-风险年检-公司列表概览
   */
  Analyze_Record_Export = 42, //
  /**
   * 导出-风险年检-维度详情
   */
  Analyze_Dimension_Detail = 43, //
  /**
   * 导出-招标排查报告-pdf
   */
  Tender_Report_Export = 44, // 招标排查报告导出
}
