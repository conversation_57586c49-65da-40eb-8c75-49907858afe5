import { HttpClient } from '@/utils/http-client';

export const createService = (httpClient: HttpClient) => ({
  getPersonId(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/person/get-person-id', params);
  },
  getRelatCompany(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/people/getRelatCompany', {
      params,
    });
  },
  batchSearch(data): Promise<any> {
    return httpClient.post(`/batch/boi/search`, data);
  },
  batchDetail(params): Promise<any> {
    return httpClient.get(`/batch/boi/detail`, { params });
  },
  batchHistory(data): Promise<any> {
    return httpClient.post(`/batch/boi/history`, data);
  },
  historyStatistics(data): Promise<any> {
    return httpClient.post('/batch/boi/statistics', data);
  },
  dataBatch(data): Promise<any> {
    return httpClient.post(`/batch/boi/import/data`, data);
  },
  getSingleDetail(params: { companyKey: string; keyNo?: string }): Promise<any> {
    return httpClient.post(`/boi`, params);
  },
  getVerificationRecord(params): Promise<Readonly<any>> {
    return httpClient.post('/boi/history', params);
  },
});
